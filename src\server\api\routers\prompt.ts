import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure, publicProcedure } from '@/server/api/trpc'

export const promptRouter = createTRPCRouter({
  // Get public prompts
  getPublicPrompts: publicProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
        category: z.string().optional(),
        search: z.string().optional(),
        tags: z.array(z.string()).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const where: any = {
        isPublic: true,
        status: 'PUBLISHED',
      }

      if (input.category) {
        where.category = input.category
      }

      if (input.search) {
        where.OR = [
          { title: { contains: input.search, mode: 'insensitive' } },
          { description: { contains: input.search, mode: 'insensitive' } },
          { content: { contains: input.search, mode: 'insensitive' } },
        ]
      }

      if (input.tags && input.tags.length > 0) {
        where.tags = {
          hasSome: input.tags,
        }
      }

      const [prompts, total] = await Promise.all([
        ctx.db.prompt.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: input.limit,
          skip: input.offset,
        }),
        ctx.db.prompt.count({ where }),
      ])

      return {
        prompts,
        total,
        hasMore: input.offset + input.limit < total,
      }
    }),

  // Get user's prompts
  getUserPrompts: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
        status: z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED']).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const where: any = {
        userId: ctx.session.user.id,
      }

      if (input.status) {
        where.status = input.status
      }

      const [prompts, total] = await Promise.all([
        ctx.db.prompt.findMany({
          where,
          orderBy: { updatedAt: 'desc' },
          take: input.limit,
          skip: input.offset,
        }),
        ctx.db.prompt.count({ where }),
      ])

      return {
        prompts,
        total,
        hasMore: input.offset + input.limit < total,
      }
    }),

  // Get single prompt
  getPrompt: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const prompt = await ctx.db.prompt.findFirst({
        where: {
          id: input.id,
          OR: [
            { userId: ctx.session.user.id },
            { isPublic: true, status: 'PUBLISHED' },
          ],
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      })

      if (!prompt) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Prompt not found',
        })
      }

      return prompt
    }),

  // Create prompt
  createPrompt: protectedProcedure
    .input(
      z.object({
        title: z.string().min(1).max(200),
        description: z.string().max(500).optional(),
        content: z.string().min(1),
        category: z.string().optional(),
        tags: z.array(z.string()).default([]),
        isPublic: z.boolean().default(false),
        isTemplate: z.boolean().default(false),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const prompt = await ctx.db.prompt.create({
        data: {
          title: input.title,
          description: input.description,
          content: input.content,
          category: input.category,
          tags: input.tags,
          userId: ctx.session.user.id,
          isPublic: input.isPublic,
          isTemplate: input.isTemplate,
          status: 'DRAFT',
          version: 1,
        },
      })

      // Log the action
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'PROMPT_CREATED',
          resource: 'PROMPT',
          details: {
            promptId: prompt.id,
            title: prompt.title,
            isPublic: prompt.isPublic,
          },
        },
      })

      return prompt
    }),

  // Update prompt
  updatePrompt: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        title: z.string().min(1).max(200).optional(),
        description: z.string().max(500).optional(),
        content: z.string().min(1).optional(),
        category: z.string().optional(),
        tags: z.array(z.string()).optional(),
        isPublic: z.boolean().optional(),
        isTemplate: z.boolean().optional(),
        status: z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED']).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Verify ownership
      const existingPrompt = await ctx.db.prompt.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
      })

      if (!existingPrompt) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Prompt not found',
        })
      }

      const { id, ...updateData } = input
      const prompt = await ctx.db.prompt.update({
        where: { id: input.id },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
      })

      // Log the action
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'PROMPT_UPDATED',
          resource: 'PROMPT',
          details: {
            promptId: prompt.id,
            changes: updateData,
          },
        },
      })

      return prompt
    }),

  // Delete prompt
  deletePrompt: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Verify ownership
      const existingPrompt = await ctx.db.prompt.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
      })

      if (!existingPrompt) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Prompt not found',
        })
      }

      await ctx.db.prompt.delete({
        where: { id: input.id },
      })

      // Log the action
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'PROMPT_DELETED',
          resource: 'PROMPT',
          details: {
            promptId: input.id,
            title: existingPrompt.title,
          },
        },
      })

      return { success: true }
    }),

  // Duplicate prompt
  duplicatePrompt: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const originalPrompt = await ctx.db.prompt.findFirst({
        where: {
          id: input.id,
          OR: [
            { userId: ctx.session.user.id },
            { isPublic: true, status: 'PUBLISHED' },
          ],
        },
      })

      if (!originalPrompt) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Prompt not found',
        })
      }

      const duplicatedPrompt = await ctx.db.prompt.create({
        data: {
          title: `${originalPrompt.title} (Copy)`,
          description: originalPrompt.description,
          content: originalPrompt.content,
          category: originalPrompt.category,
          tags: originalPrompt.tags,
          userId: ctx.session.user.id,
          isPublic: false, // Always private when duplicated
          isTemplate: originalPrompt.isTemplate,
          status: 'DRAFT',
          version: 1,
        },
      })

      return duplicatedPrompt
    }),

  // Get prompt categories
  getCategories: publicProcedure.query(async ({ ctx }) => {
    const categories = await ctx.db.prompt.groupBy({
      by: ['category'],
      where: {
        isPublic: true,
        status: 'PUBLISHED',
        category: {
          not: null,
        },
      },
      _count: {
        category: true,
      },
      orderBy: {
        _count: {
          category: 'desc',
        },
      },
    })

    return categories
      .filter((cat) => cat.category)
      .map((cat) => ({
        name: cat.category!,
        count: cat._count.category,
      }))
  }),

  // Get popular tags
  getPopularTags: publicProcedure
    .input(z.object({ limit: z.number().min(1).max(50).default(20) }))
    .query(async ({ ctx, input }) => {
      const prompts = await ctx.db.prompt.findMany({
        where: {
          isPublic: true,
          status: 'PUBLISHED',
          tags: {
            isEmpty: false,
          },
        },
        select: {
          tags: true,
        },
      })

      // Count tag occurrences
      const tagCounts: Record<string, number> = {}
      prompts.forEach((prompt) => {
        prompt.tags.forEach((tag) => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1
        })
      })

      // Sort by count and return top tags
      return Object.entries(tagCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, input.limit)
        .map(([tag, count]) => ({ tag, count }))
    }),

  // Search prompts
  searchPrompts: publicProcedure
    .input(
      z.object({
        query: z.string().min(1),
        limit: z.number().min(1).max(50).default(10),
      })
    )
    .query(async ({ ctx, input }) => {
      const prompts = await ctx.db.prompt.findMany({
        where: {
          isPublic: true,
          status: 'PUBLISHED',
          OR: [
            { title: { contains: input.query, mode: 'insensitive' } },
            { description: { contains: input.query, mode: 'insensitive' } },
            { content: { contains: input.query, mode: 'insensitive' } },
            { tags: { hasSome: [input.query] } },
          ],
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: input.limit,
      })

      return prompts
    }),
})
