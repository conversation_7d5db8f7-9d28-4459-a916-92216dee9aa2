import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, adminProcedure } from '@/server/api/trpc'
import { dbUtils } from '@/lib/db'

export const analyticsRouter = createTRPCRouter({
  // Get user analytics
  getUserAnalytics: protectedProcedure
    .input(
      z.object({
        days: z.number().min(1).max(365).default(30),
      })
    )
    .query(async ({ ctx, input }) => {
      const analytics = await dbUtils.getUsageAnalytics(ctx.session.user.id, input.days)
      
      // Calculate summary statistics
      const totalTokens = analytics.reduce((sum, stat) => sum + stat.tokensUsed, 0)
      const totalCost = analytics.reduce((sum, stat) => sum + stat.cost, 0)
      const totalMessages = analytics.reduce((sum, stat) => sum + stat.messagesCount, 0)

      // Group by provider
      const providerStats = analytics.reduce((acc, stat) => {
        if (!acc[stat.provider]) {
          acc[stat.provider] = {
            tokens: 0,
            cost: 0,
            messages: 0,
          }
        }
        acc[stat.provider].tokens += stat.tokensUsed
        acc[stat.provider].cost += stat.cost
        acc[stat.provider].messages += stat.messagesCount
        return acc
      }, {} as Record<string, { tokens: number; cost: number; messages: number }>)

      // Group by model
      const modelStats = analytics.reduce((acc, stat) => {
        if (!acc[stat.model]) {
          acc[stat.model] = {
            tokens: 0,
            cost: 0,
            messages: 0,
          }
        }
        acc[stat.model].tokens += stat.tokensUsed
        acc[stat.model].cost += stat.cost
        acc[stat.model].messages += stat.messagesCount
        return acc
      }, {} as Record<string, { tokens: number; cost: number; messages: number }>)

      // Daily breakdown
      const dailyStats = analytics.map(stat => ({
        date: stat.date,
        tokens: stat.tokensUsed,
        cost: stat.cost,
        messages: stat.messagesCount,
        provider: stat.provider,
        model: stat.model,
      }))

      return {
        summary: {
          totalTokens,
          totalCost,
          totalMessages,
          averageCostPerMessage: totalMessages > 0 ? totalCost / totalMessages : 0,
          averageTokensPerMessage: totalMessages > 0 ? totalTokens / totalMessages : 0,
        },
        providerStats,
        modelStats,
        dailyStats,
        timeRange: {
          start: new Date(Date.now() - input.days * 24 * 60 * 60 * 1000),
          end: new Date(),
        },
      }
    }),

  // Get conversation analytics
  getConversationAnalytics: protectedProcedure
    .input(
      z.object({
        days: z.number().min(1).max(365).default(30),
      })
    )
    .query(async ({ ctx, input }) => {
      const startDate = new Date(Date.now() - input.days * 24 * 60 * 60 * 1000)

      const [totalConversations, activeConversations, conversationsByProvider] = await Promise.all([
        ctx.db.conversation.count({
          where: {
            userId: ctx.session.user.id,
            createdAt: { gte: startDate },
          },
        }),
        ctx.db.conversation.count({
          where: {
            userId: ctx.session.user.id,
            status: 'ACTIVE',
            updatedAt: { gte: startDate },
          },
        }),
        ctx.db.conversation.groupBy({
          by: ['provider'],
          where: {
            userId: ctx.session.user.id,
            createdAt: { gte: startDate },
          },
          _count: {
            provider: true,
          },
        }),
      ])

      // Get average messages per conversation
      const conversationsWithMessages = await ctx.db.conversation.findMany({
        where: {
          userId: ctx.session.user.id,
          createdAt: { gte: startDate },
        },
        include: {
          _count: {
            select: { messages: true },
          },
        },
      })

      const averageMessagesPerConversation = conversationsWithMessages.length > 0
        ? conversationsWithMessages.reduce((sum, conv) => sum + conv._count.messages, 0) / conversationsWithMessages.length
        : 0

      return {
        totalConversations,
        activeConversations,
        averageMessagesPerConversation,
        conversationsByProvider: conversationsByProvider.map(item => ({
          provider: item.provider,
          count: item._count.provider,
        })),
      }
    }),

  // Get prompt analytics
  getPromptAnalytics: protectedProcedure.query(async ({ ctx }) => {
    const [totalPrompts, publicPrompts, templatePrompts, promptsByCategory] = await Promise.all([
      ctx.db.prompt.count({
        where: { userId: ctx.session.user.id },
      }),
      ctx.db.prompt.count({
        where: {
          userId: ctx.session.user.id,
          isPublic: true,
        },
      }),
      ctx.db.prompt.count({
        where: {
          userId: ctx.session.user.id,
          isTemplate: true,
        },
      }),
      ctx.db.prompt.groupBy({
        by: ['category'],
        where: {
          userId: ctx.session.user.id,
          category: { not: null },
        },
        _count: {
          category: true,
        },
      }),
    ])

    return {
      totalPrompts,
      publicPrompts,
      templatePrompts,
      promptsByCategory: promptsByCategory.map(item => ({
        category: item.category!,
        count: item._count.category,
      })),
    }
  }),

  // Get system analytics (admin only)
  getSystemAnalytics: adminProcedure
    .input(
      z.object({
        days: z.number().min(1).max(365).default(30),
      })
    )
    .query(async ({ ctx, input }) => {
      const analytics = await dbUtils.getSystemAnalytics(input.days)
      return analytics
    }),

  // Get user growth analytics (admin only)
  getUserGrowthAnalytics: adminProcedure
    .input(
      z.object({
        days: z.number().min(1).max(365).default(30),
      })
    )
    .query(async ({ ctx, input }) => {
      const startDate = new Date(Date.now() - input.days * 24 * 60 * 60 * 1000)

      const userGrowth = await ctx.db.user.groupBy({
        by: ['createdAt'],
        where: {
          createdAt: { gte: startDate },
        },
        _count: {
          createdAt: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      })

      // Group by day
      const dailyGrowth = userGrowth.reduce((acc, item) => {
        const date = item.createdAt.toISOString().split('T')[0]
        acc[date] = (acc[date] || 0) + item._count.createdAt
        return acc
      }, {} as Record<string, number>)

      return {
        dailyGrowth,
        totalNewUsers: Object.values(dailyGrowth).reduce((sum, count) => sum + count, 0),
      }
    }),

  // Get revenue analytics (admin only)
  getRevenueAnalytics: adminProcedure
    .input(
      z.object({
        days: z.number().min(1).max(365).default(30),
      })
    )
    .query(async ({ ctx, input }) => {
      const startDate = new Date(Date.now() - input.days * 24 * 60 * 60 * 1000)

      const usageStats = await ctx.db.usageStats.findMany({
        where: {
          date: { gte: startDate },
        },
        include: {
          user: {
            select: {
              plan: true,
            },
          },
        },
      })

      const totalRevenue = usageStats.reduce((sum, stat) => sum + stat.cost, 0)
      
      const revenueByPlan = usageStats.reduce((acc, stat) => {
        const plan = stat.user.plan
        acc[plan] = (acc[plan] || 0) + stat.cost
        return acc
      }, {} as Record<string, number>)

      const revenueByProvider = usageStats.reduce((acc, stat) => {
        const provider = stat.provider
        acc[provider] = (acc[provider] || 0) + stat.cost
        return acc
      }, {} as Record<string, number>)

      return {
        totalRevenue,
        revenueByPlan,
        revenueByProvider,
        averageRevenuePerUser: usageStats.length > 0 ? totalRevenue / new Set(usageStats.map(s => s.userId)).size : 0,
      }
    }),

  // Export analytics data
  exportAnalytics: protectedProcedure
    .input(
      z.object({
        type: z.enum(['usage', 'conversations', 'prompts']),
        format: z.enum(['json', 'csv']).default('json'),
        days: z.number().min(1).max(365).default(30),
      })
    )
    .mutation(async ({ ctx, input }) => {
      let data: any

      switch (input.type) {
        case 'usage':
          data = await dbUtils.getUsageAnalytics(ctx.session.user.id, input.days)
          break
        case 'conversations':
          data = await ctx.db.conversation.findMany({
            where: {
              userId: ctx.session.user.id,
              createdAt: {
                gte: new Date(Date.now() - input.days * 24 * 60 * 60 * 1000),
              },
            },
            include: {
              _count: {
                select: { messages: true },
              },
            },
          })
          break
        case 'prompts':
          data = await ctx.db.prompt.findMany({
            where: { userId: ctx.session.user.id },
          })
          break
      }

      // Log the export
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'ANALYTICS_EXPORTED',
          resource: 'ANALYTICS',
          details: {
            type: input.type,
            format: input.format,
            days: input.days,
            recordCount: Array.isArray(data) ? data.length : 1,
          },
        },
      })

      return {
        data,
        exportedAt: new Date(),
        format: input.format,
        type: input.type,
      }
    }),
})
