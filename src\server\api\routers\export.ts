import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure } from '@/server/api/trpc'

export const exportRouter = createTRPCRouter({
  // Export conversation
  exportConversation: protectedProcedure
    .input(
      z.object({
        conversationId: z.string(),
        format: z.enum(['JSON', 'MARKDOWN', 'PDF', 'NOTION', 'OBSIDIAN', 'GOOGLE_DOCS']),
        options: z
          .object({
            includeMetadata: z.boolean().default(true),
            includeTimestamps: z.boolean().default(true),
            includeBranches: z.boolean().default(false),
            template: z.string().optional(),
          })
          .optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Verify conversation ownership
      const conversation = await ctx.db.conversation.findFirst({
        where: {
          id: input.conversationId,
          userId: ctx.session.user.id,
        },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
          },
          branches: input.options?.includeBranches
            ? {
                include: {
                  messages: {
                    orderBy: { createdAt: 'asc' },
                  },
                },
              }
            : false,
          user: {
            select: {
              name: true,
              email: true,
            },
          },
        },
      })

      if (!conversation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Conversation not found',
        })
      }

      // Create export record
      const exportRecord = await ctx.db.export.create({
        data: {
          conversationId: input.conversationId,
          format: input.format,
          destination: 'download', // For now, all exports are downloads
          status: 'PROCESSING',
          metadata: {
            options: input.options,
            requestedBy: ctx.session.user.id,
          },
        },
      })

      try {
        let exportData: any

        switch (input.format) {
          case 'JSON':
            exportData = {
              conversation: {
                id: conversation.id,
                title: conversation.title,
                provider: conversation.provider,
                model: conversation.model,
                createdAt: conversation.createdAt,
                updatedAt: conversation.updatedAt,
                ...(input.options?.includeMetadata && { metadata: conversation.metadata }),
              },
              messages: conversation.messages.map((msg) => ({
                id: msg.id,
                role: msg.role,
                content: msg.content,
                ...(input.options?.includeTimestamps && {
                  createdAt: msg.createdAt,
                  updatedAt: msg.updatedAt,
                }),
                ...(input.options?.includeMetadata && { metadata: msg.metadata }),
              })),
              ...(input.options?.includeBranches &&
                conversation.branches && {
                  branches: conversation.branches.map((branch) => ({
                    id: branch.id,
                    title: branch.title,
                    branchName: branch.branchName,
                    messages: branch.messages.map((msg) => ({
                      id: msg.id,
                      role: msg.role,
                      content: msg.content,
                      ...(input.options?.includeTimestamps && {
                        createdAt: msg.createdAt,
                        updatedAt: msg.updatedAt,
                      }),
                      ...(input.options?.includeMetadata && { metadata: msg.metadata }),
                    })),
                  })),
                }),
              exportedAt: new Date(),
              exportedBy: conversation.user.name,
            }
            break

          case 'MARKDOWN':
            let markdown = `# ${conversation.title}\n\n`
            
            if (input.options?.includeMetadata) {
              markdown += `**Provider:** ${conversation.provider}\n`
              markdown += `**Model:** ${conversation.model}\n`
              markdown += `**Created:** ${conversation.createdAt.toISOString()}\n\n`
            }

            markdown += `---\n\n`

            for (const message of conversation.messages) {
              const role = message.role === 'USER' ? '👤 User' : '🤖 Assistant'
              markdown += `## ${role}\n\n`
              
              if (input.options?.includeTimestamps) {
                markdown += `*${message.createdAt.toISOString()}*\n\n`
              }
              
              markdown += `${message.content}\n\n`
              markdown += `---\n\n`
            }

            if (input.options?.includeBranches && conversation.branches) {
              for (const branch of conversation.branches) {
                markdown += `# Branch: ${branch.branchName}\n\n`
                
                for (const message of branch.messages) {
                  const role = message.role === 'USER' ? '👤 User' : '🤖 Assistant'
                  markdown += `## ${role}\n\n`
                  
                  if (input.options?.includeTimestamps) {
                    markdown += `*${message.createdAt.toISOString()}*\n\n`
                  }
                  
                  markdown += `${message.content}\n\n`
                  markdown += `---\n\n`
                }
              }
            }

            exportData = markdown
            break

          case 'PDF':
            // For PDF export, we'd typically use a library like puppeteer or jsPDF
            // For now, we'll return a placeholder
            exportData = {
              message: 'PDF export would be generated here',
              conversation: conversation.title,
              messageCount: conversation.messages.length,
            }
            break

          case 'NOTION':
          case 'OBSIDIAN':
          case 'GOOGLE_DOCS':
            // These would require API integrations with respective services
            exportData = {
              message: `${input.format} export would be implemented with API integration`,
              conversation: conversation.title,
              messageCount: conversation.messages.length,
            }
            break

          default:
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: 'Unsupported export format',
            })
        }

        // Update export record as completed
        await ctx.db.export.update({
          where: { id: exportRecord.id },
          data: {
            status: 'COMPLETED',
            completedAt: new Date(),
            metadata: {
              ...exportRecord.metadata,
              dataSize: JSON.stringify(exportData).length,
            },
          },
        })

        // Log the export
        await ctx.db.auditLog.create({
          data: {
            userId: ctx.session.user.id,
            action: 'CONVERSATION_EXPORTED',
            resource: 'CONVERSATION',
            details: {
              conversationId: input.conversationId,
              format: input.format,
              exportId: exportRecord.id,
              options: input.options,
            },
          },
        })

        return {
          exportId: exportRecord.id,
          data: exportData,
          format: input.format,
          filename: `${conversation.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.${input.format.toLowerCase()}`,
        }
      } catch (error) {
        // Update export record as failed
        await ctx.db.export.update({
          where: { id: exportRecord.id },
          data: {
            status: 'FAILED',
            metadata: {
              ...exportRecord.metadata,
              error: error instanceof Error ? error.message : 'Unknown error',
            },
          },
        })

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Export failed',
        })
      }
    }),

  // Get export history
  getExportHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      })
    )
    .query(async ({ ctx, input }) => {
      const exports = await ctx.db.export.findMany({
        where: {
          conversation: {
            userId: ctx.session.user.id,
          },
        },
        include: {
          conversation: {
            select: {
              id: true,
              title: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: input.limit,
        skip: input.offset,
      })

      const total = await ctx.db.export.count({
        where: {
          conversation: {
            userId: ctx.session.user.id,
          },
        },
      })

      return {
        exports,
        total,
        hasMore: input.offset + input.limit < total,
      }
    }),

  // Get export status
  getExportStatus: protectedProcedure
    .input(z.object({ exportId: z.string() }))
    .query(async ({ ctx, input }) => {
      const exportRecord = await ctx.db.export.findFirst({
        where: {
          id: input.exportId,
          conversation: {
            userId: ctx.session.user.id,
          },
        },
        include: {
          conversation: {
            select: {
              title: true,
            },
          },
        },
      })

      if (!exportRecord) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Export not found',
        })
      }

      return exportRecord
    }),

  // Delete export
  deleteExport: protectedProcedure
    .input(z.object({ exportId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const exportRecord = await ctx.db.export.findFirst({
        where: {
          id: input.exportId,
          conversation: {
            userId: ctx.session.user.id,
          },
        },
      })

      if (!exportRecord) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Export not found',
        })
      }

      await ctx.db.export.delete({
        where: { id: input.exportId },
      })

      return { success: true }
    }),

  // Bulk export conversations
  bulkExportConversations: protectedProcedure
    .input(
      z.object({
        conversationIds: z.array(z.string()).min(1).max(10),
        format: z.enum(['JSON', 'MARKDOWN']),
        options: z
          .object({
            includeMetadata: z.boolean().default(true),
            includeTimestamps: z.boolean().default(true),
            includeBranches: z.boolean().default(false),
          })
          .optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Verify all conversations belong to the user
      const conversations = await ctx.db.conversation.findMany({
        where: {
          id: { in: input.conversationIds },
          userId: ctx.session.user.id,
        },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
          },
        },
      })

      if (conversations.length !== input.conversationIds.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Some conversations not found',
        })
      }

      let exportData: any

      switch (input.format) {
        case 'JSON':
          exportData = {
            conversations: conversations.map((conv) => ({
              id: conv.id,
              title: conv.title,
              provider: conv.provider,
              model: conv.model,
              createdAt: conv.createdAt,
              updatedAt: conv.updatedAt,
              ...(input.options?.includeMetadata && { metadata: conv.metadata }),
              messages: conv.messages.map((msg) => ({
                id: msg.id,
                role: msg.role,
                content: msg.content,
                ...(input.options?.includeTimestamps && {
                  createdAt: msg.createdAt,
                  updatedAt: msg.updatedAt,
                }),
                ...(input.options?.includeMetadata && { metadata: msg.metadata }),
              })),
            })),
            exportedAt: new Date(),
            totalConversations: conversations.length,
          }
          break

        case 'MARKDOWN':
          let markdown = `# Bulk Export - ${conversations.length} Conversations\n\n`
          markdown += `Exported on: ${new Date().toISOString()}\n\n`
          markdown += `---\n\n`

          for (const conversation of conversations) {
            markdown += `# ${conversation.title}\n\n`
            
            if (input.options?.includeMetadata) {
              markdown += `**Provider:** ${conversation.provider}\n`
              markdown += `**Model:** ${conversation.model}\n`
              markdown += `**Created:** ${conversation.createdAt.toISOString()}\n\n`
            }

            for (const message of conversation.messages) {
              const role = message.role === 'USER' ? '👤 User' : '🤖 Assistant'
              markdown += `## ${role}\n\n`
              
              if (input.options?.includeTimestamps) {
                markdown += `*${message.createdAt.toISOString()}*\n\n`
              }
              
              markdown += `${message.content}\n\n`
              markdown += `---\n\n`
            }

            markdown += `\n\n`
          }

          exportData = markdown
          break

        default:
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Unsupported export format for bulk export',
          })
      }

      // Log the bulk export
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'BULK_EXPORT',
          resource: 'CONVERSATION',
          details: {
            conversationIds: input.conversationIds,
            format: input.format,
            conversationCount: conversations.length,
            options: input.options,
          },
        },
      })

      return {
        data: exportData,
        format: input.format,
        conversationCount: conversations.length,
        filename: `bulk_export_${conversations.length}_conversations_${new Date().toISOString().split('T')[0]}.${input.format.toLowerCase()}`,
      }
    }),
})
