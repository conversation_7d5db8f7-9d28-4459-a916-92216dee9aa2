@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --font-inter: 'Inter', system-ui, sans-serif;
    --font-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--foreground));
  }

  /* Chat message styles */
  .message-content {
    @apply prose prose-sm max-w-none dark:prose-invert;
  }

  .message-content pre {
    @apply bg-muted rounded-md p-4 overflow-x-auto;
  }

  .message-content code {
    @apply bg-muted px-1 py-0.5 rounded text-sm;
  }

  .message-content pre code {
    @apply bg-transparent p-0;
  }

  /* Loading animation */
  .loading-dots {
    @apply inline-flex space-x-1;
  }

  .loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full animate-pulse;
    animation-delay: calc(var(--i) * 0.2s);
  }

  /* Gradient backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 50%, hsl(var(--accent)) 100%);
  }

  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Conversation tree styles */
  .conversation-tree {
    @apply relative;
  }

  .conversation-tree::before {
    @apply absolute left-4 top-0 bottom-0 w-px bg-border;
    content: '';
  }

  .conversation-branch {
    @apply relative pl-8;
  }

  .conversation-branch::before {
    @apply absolute left-4 top-4 w-4 h-px bg-border;
    content: '';
  }

  /* Syntax highlighting overrides */
  .hljs {
    @apply bg-muted !important;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Animation utilities */
  .animate-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-out {
    animation: fadeOut 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeOut {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(-10px);
    }
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .mobile-optimized {
      @apply text-sm leading-relaxed;
    }
  }

  /* Print styles */
  @media print {
    .no-print {
      @apply hidden;
    }
    
    .print-break {
      page-break-before: always;
    }
  }
}
