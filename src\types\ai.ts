export interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
  name?: string
  metadata?: Record<string, any>
}

export interface ChatResponse {
  content: string
  usage: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  model: string
  provider: string
  finishReason?: string
  stream?: any
  metadata?: Record<string, any>
}

export interface ModelInfo {
  id: string
  name: string
  provider: string
  contextLength: number
  pricing: {
    input: number  // per 1K tokens
    output: number // per 1K tokens
  }
  capabilities?: {
    streaming?: boolean
    images?: boolean
    functions?: boolean
    vision?: boolean
  }
  description?: string
}

export interface AIProvider {
  chat(
    messages: ChatMessage[],
    options?: {
      model?: string
      temperature?: number
      maxTokens?: number
      stream?: boolean
      functions?: any[]
      functionCall?: any
    }
  ): Promise<ChatResponse>

  getModels(): Promise<ModelInfo[]>
  calculateCost(usage: { promptTokens: number; completionTokens: number }, model: string): number
  validateApiKey(apiKey?: string): Promise<boolean>
  getProviderName(): string
  getProviderIcon(): string
  supportsStreaming(): boolean
  supportsImages(): boolean
  supportsFunctions(): boolean
  getMaxTokens(model: string): number
  estimateTokens(text: string): Promise<number>
}

export interface AIProviderConfig {
  name: string
  provider: string
  apiKey: string
  baseUrl?: string
  models: string[]
  enabled: boolean
  priority: number
  rateLimits?: {
    requestsPerMinute: number
    tokensPerMinute: number
  }
}

export interface ConversationContext {
  messages: ChatMessage[]
  systemPrompt?: string
  temperature?: number
  maxTokens?: number
  model: string
  provider: string
  metadata?: Record<string, any>
}

export interface PromptTemplate {
  id: string
  name: string
  description?: string
  template: string
  variables: PromptVariable[]
  category?: string
  tags: string[]
  isPublic: boolean
  usage: {
    count: number
    rating: number
  }
  metadata?: Record<string, any>
}

export interface PromptVariable {
  name: string
  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect'
  description?: string
  required: boolean
  defaultValue?: any
  options?: string[] // for select/multiselect
  validation?: {
    min?: number
    max?: number
    pattern?: string
  }
}

export interface AIModelRouting {
  strategy: 'cost' | 'speed' | 'quality' | 'custom'
  fallbackChain: string[]
  costThreshold?: number
  speedThreshold?: number
  qualityThreshold?: number
  customRules?: RoutingRule[]
}

export interface RoutingRule {
  condition: {
    messageLength?: { min?: number; max?: number }
    conversationLength?: { min?: number; max?: number }
    userPlan?: string[]
    timeOfDay?: { start: string; end: string }
    contentType?: string[]
  }
  action: {
    preferredModels: string[]
    maxCost?: number
    priority: number
  }
}

export interface AIAnalytics {
  usage: {
    totalRequests: number
    totalTokens: number
    totalCost: number
    averageResponseTime: number
  }
  models: {
    [modelId: string]: {
      requests: number
      tokens: number
      cost: number
      averageResponseTime: number
      successRate: number
    }
  }
  providers: {
    [providerId: string]: {
      requests: number
      tokens: number
      cost: number
      averageResponseTime: number
      successRate: number
      uptime: number
    }
  }
  timeRange: {
    start: Date
    end: Date
  }
}

export interface ConversationBranch {
  id: string
  name: string
  parentId?: string
  messages: ChatMessage[]
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface ABTestConfig {
  id: string
  name: string
  description?: string
  variants: ABTestVariant[]
  trafficSplit: number[] // percentages for each variant
  metrics: string[] // metrics to track
  status: 'draft' | 'running' | 'completed' | 'paused'
  startDate?: Date
  endDate?: Date
}

export interface ABTestVariant {
  id: string
  name: string
  prompt: string
  model?: string
  temperature?: number
  maxTokens?: number
  metadata?: Record<string, any>
}

export interface ABTestResult {
  variantId: string
  metrics: {
    [metricName: string]: {
      value: number
      confidence: number
      sampleSize: number
    }
  }
  conversions: number
  impressions: number
  conversionRate: number
}

export interface ContextWindow {
  maxTokens: number
  currentTokens: number
  messages: ChatMessage[]
  strategy: 'truncate' | 'summarize' | 'sliding'
  reservedTokens: number // for response
}

export interface SmartSummarization {
  strategy: 'extractive' | 'abstractive' | 'hybrid'
  compressionRatio: number
  preserveContext: boolean
  keyTopics: string[]
  summary: string
  originalLength: number
  summaryLength: number
}

export interface VoiceConfig {
  enabled: boolean
  provider: 'browser' | 'openai' | 'elevenlabs'
  voice: string
  speed: number
  pitch: number
  language: string
  autoPlay: boolean
}

export interface ExportConfig {
  format: 'json' | 'markdown' | 'pdf' | 'docx'
  destination: 'download' | 'notion' | 'obsidian' | 'google-docs'
  options: {
    includeMetadata?: boolean
    includeTimestamps?: boolean
    includeBranches?: boolean
    template?: string
  }
}

export interface CollaborationSettings {
  enabled: boolean
  permissions: {
    [userId: string]: 'read' | 'comment' | 'edit' | 'admin'
  }
  realTimeSync: boolean
  commentingEnabled: boolean
  versionHistory: boolean
}

export interface PluginManifest {
  id: string
  name: string
  version: string
  description: string
  author: string
  permissions: string[]
  entryPoint: string
  hooks: string[]
  settings?: PluginSetting[]
}

export interface PluginSetting {
  key: string
  name: string
  type: 'text' | 'number' | 'boolean' | 'select'
  description?: string
  required: boolean
  defaultValue?: any
  options?: string[]
}
