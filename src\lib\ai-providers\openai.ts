import OpenAI from 'openai'
import { AIProvider, ChatMessage, ChatResponse, ModelInfo } from '@/types/ai'

export class OpenAIProvider implements AIProvider {
  private client: OpenAI
  private apiKey: string

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.OPENAI_API_KEY!
    this.client = new OpenAI({
      apiKey: this.apiKey,
    })
  }

  async chat(messages: ChatMessage[], options: {
    model?: string
    temperature?: number
    maxTokens?: number
    stream?: boolean
  } = {}): Promise<ChatResponse> {
    try {
      const {
        model = 'gpt-4-turbo-preview',
        temperature = 0.7,
        maxTokens = 4000,
        stream = false,
      } = options

      const openAIMessages = messages.map(msg => ({
        role: msg.role as 'system' | 'user' | 'assistant',
        content: msg.content,
      }))

      if (stream) {
        const stream = await this.client.chat.completions.create({
          model,
          messages: openAIMessages,
          temperature,
          max_tokens: maxTokens,
          stream: true,
        })

        return {
          content: '',
          stream,
          usage: {
            promptTokens: 0,
            completionTokens: 0,
            totalTokens: 0,
          },
          model,
          provider: 'openai',
        }
      }

      const completion = await this.client.chat.completions.create({
        model,
        messages: openAIMessages,
        temperature,
        max_tokens: maxTokens,
      })

      const choice = completion.choices[0]
      if (!choice?.message?.content) {
        throw new Error('No response from OpenAI')
      }

      return {
        content: choice.message.content,
        usage: {
          promptTokens: completion.usage?.prompt_tokens || 0,
          completionTokens: completion.usage?.completion_tokens || 0,
          totalTokens: completion.usage?.total_tokens || 0,
        },
        model,
        provider: 'openai',
        finishReason: choice.finish_reason,
      }
    } catch (error) {
      console.error('OpenAI API error:', error)
      throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async getModels(): Promise<ModelInfo[]> {
    try {
      const models = await this.client.models.list()
      
      return models.data
        .filter(model => model.id.includes('gpt'))
        .map(model => ({
          id: model.id,
          name: model.id,
          provider: 'openai',
          contextLength: this.getContextLength(model.id),
          pricing: this.getPricing(model.id),
        }))
        .sort((a, b) => a.name.localeCompare(b.name))
    } catch (error) {
      console.error('Error fetching OpenAI models:', error)
      return this.getDefaultModels()
    }
  }

  private getContextLength(modelId: string): number {
    const contextLengths: Record<string, number> = {
      'gpt-4-turbo-preview': 128000,
      'gpt-4-turbo': 128000,
      'gpt-4': 8192,
      'gpt-4-32k': 32768,
      'gpt-3.5-turbo': 16385,
      'gpt-3.5-turbo-16k': 16385,
    }

    return contextLengths[modelId] || 4096
  }

  private getPricing(modelId: string): { input: number; output: number } {
    // Pricing per 1K tokens (as of 2024)
    const pricing: Record<string, { input: number; output: number }> = {
      'gpt-4-turbo-preview': { input: 0.01, output: 0.03 },
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-4-32k': { input: 0.06, output: 0.12 },
      'gpt-3.5-turbo': { input: 0.0005, output: 0.0015 },
      'gpt-3.5-turbo-16k': { input: 0.003, output: 0.004 },
    }

    return pricing[modelId] || { input: 0.001, output: 0.002 }
  }

  private getDefaultModels(): ModelInfo[] {
    return [
      {
        id: 'gpt-4-turbo-preview',
        name: 'GPT-4 Turbo Preview',
        provider: 'openai',
        contextLength: 128000,
        pricing: { input: 0.01, output: 0.03 },
      },
      {
        id: 'gpt-4',
        name: 'GPT-4',
        provider: 'openai',
        contextLength: 8192,
        pricing: { input: 0.03, output: 0.06 },
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        provider: 'openai',
        contextLength: 16385,
        pricing: { input: 0.0005, output: 0.0015 },
      },
    ]
  }

  calculateCost(usage: { promptTokens: number; completionTokens: number }, model: string): number {
    const pricing = this.getPricing(model)
    const inputCost = (usage.promptTokens / 1000) * pricing.input
    const outputCost = (usage.completionTokens / 1000) * pricing.output
    return inputCost + outputCost
  }

  async validateApiKey(apiKey?: string): Promise<boolean> {
    try {
      const testClient = new OpenAI({
        apiKey: apiKey || this.apiKey,
      })

      await testClient.models.list()
      return true
    } catch (error) {
      return false
    }
  }

  getProviderName(): string {
    return 'OpenAI'
  }

  getProviderIcon(): string {
    return '🤖'
  }

  supportsStreaming(): boolean {
    return true
  }

  supportsImages(): boolean {
    return true
  }

  supportsFunctions(): boolean {
    return true
  }

  getMaxTokens(model: string): number {
    return this.getContextLength(model)
  }

  async estimateTokens(text: string): Promise<number> {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4)
  }

  async moderateContent(content: string): Promise<{
    flagged: boolean
    categories: string[]
    scores: Record<string, number>
  }> {
    try {
      const moderation = await this.client.moderations.create({
        input: content,
      })

      const result = moderation.results[0]
      if (!result) {
        return { flagged: false, categories: [], scores: {} }
      }

      const flaggedCategories = Object.entries(result.categories)
        .filter(([_, flagged]) => flagged)
        .map(([category]) => category)

      return {
        flagged: result.flagged,
        categories: flaggedCategories,
        scores: result.category_scores,
      }
    } catch (error) {
      console.error('Content moderation error:', error)
      return { flagged: false, categories: [], scores: {} }
    }
  }
}
