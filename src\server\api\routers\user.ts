import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure, adminProcedure } from '@/server/api/trpc'

export const userRouter = createTRPCRouter({
  // Get current user's dashboard data
  getDashboard: protectedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session.user.id

    const [
      user,
      recentConversations,
      usageStats,
      promptCount,
      workspaceCount,
    ] = await Promise.all([
      ctx.db.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          role: true,
          plan: true,
          credits: true,
          createdAt: true,
        },
      }),
      ctx.db.conversation.findMany({
        where: {
          userId,
          status: 'ACTIVE',
        },
        include: {
          messages: {
            take: 1,
            orderBy: { createdAt: 'desc' },
          },
          _count: {
            select: { messages: true },
          },
        },
        orderBy: { updatedAt: 'desc' },
        take: 5,
      }),
      ctx.db.usageStats.findMany({
        where: {
          userId,
          date: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
          },
        },
        orderBy: { date: 'desc' },
      }),
      ctx.db.prompt.count({
        where: { userId },
      }),
      ctx.db.workspaceMember.count({
        where: { userId },
      }),
    ])

    if (!user) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'User not found',
      })
    }

    // Calculate usage summary
    const totalTokens = usageStats.reduce((sum, stat) => sum + stat.tokensUsed, 0)
    const totalCost = usageStats.reduce((sum, stat) => sum + stat.cost, 0)
    const totalMessages = usageStats.reduce((sum, stat) => sum + stat.messagesCount, 0)

    return {
      user,
      stats: {
        conversations: recentConversations.length,
        prompts: promptCount,
        workspaces: workspaceCount,
        tokensUsed: totalTokens,
        totalCost,
        totalMessages,
      },
      recentConversations,
      usageStats,
    }
  }),

  // Update user credits (admin only)
  updateCredits: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        credits: z.number().min(0),
        reason: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.update({
        where: { id: input.userId },
        data: { credits: input.credits },
      })

      // Log the action
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'CREDITS_UPDATED',
          resource: 'USER',
          details: {
            targetUserId: input.userId,
            newCredits: input.credits,
            reason: input.reason,
          },
        },
      })

      return user
    }),

  // Update user plan (admin only)
  updatePlan: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        plan: z.enum(['FREE', 'PRO', 'ENTERPRISE']),
        reason: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.update({
        where: { id: input.userId },
        data: { plan: input.plan },
      })

      // Log the action
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'PLAN_UPDATED',
          resource: 'USER',
          details: {
            targetUserId: input.userId,
            newPlan: input.plan,
            reason: input.reason,
          },
        },
      })

      return user
    }),

  // Get all users (admin only)
  getAllUsers: adminProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
        search: z.string().optional(),
        role: z.enum(['USER', 'ADMIN', 'SUPER_ADMIN']).optional(),
        plan: z.enum(['FREE', 'PRO', 'ENTERPRISE']).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const where: any = {}

      if (input.search) {
        where.OR = [
          { name: { contains: input.search, mode: 'insensitive' } },
          { email: { contains: input.search, mode: 'insensitive' } },
        ]
      }

      if (input.role) {
        where.role = input.role
      }

      if (input.plan) {
        where.plan = input.plan
      }

      const [users, total] = await Promise.all([
        ctx.db.user.findMany({
          where,
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true,
            plan: true,
            credits: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: {
                conversations: true,
                prompts: true,
                workspaces: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: input.limit,
          skip: input.offset,
        }),
        ctx.db.user.count({ where }),
      ])

      return {
        users,
        total,
        hasMore: input.offset + input.limit < total,
      }
    }),

  // Get user details (admin only)
  getUserDetails: adminProcedure
    .input(z.object({ userId: z.string() }))
    .query(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: input.userId },
        include: {
          conversations: {
            orderBy: { updatedAt: 'desc' },
            take: 10,
            include: {
              _count: {
                select: { messages: true },
              },
            },
          },
          prompts: {
            orderBy: { updatedAt: 'desc' },
            take: 10,
          },
          usageStats: {
            orderBy: { date: 'desc' },
            take: 30,
          },
          auditLogs: {
            orderBy: { createdAt: 'desc' },
            take: 20,
          },
          _count: {
            select: {
              conversations: true,
              prompts: true,
              workspaces: true,
              usageStats: true,
              auditLogs: true,
            },
          },
        },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      return user
    }),

  // Update user role (admin only)
  updateRole: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        role: z.enum(['USER', 'ADMIN', 'SUPER_ADMIN']),
        reason: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Prevent self-demotion from SUPER_ADMIN
      if (
        ctx.session.user.id === input.userId &&
        ctx.session.user.role === 'SUPER_ADMIN' &&
        input.role !== 'SUPER_ADMIN'
      ) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Cannot demote yourself from SUPER_ADMIN',
        })
      }

      const user = await ctx.db.user.update({
        where: { id: input.userId },
        data: { role: input.role },
      })

      // Log the action
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'ROLE_UPDATED',
          resource: 'USER',
          details: {
            targetUserId: input.userId,
            newRole: input.role,
            reason: input.reason,
          },
        },
      })

      return user
    }),

  // Suspend user (admin only)
  suspendUser: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        reason: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // In a real app, you'd add a 'suspended' field to the user model
      // For now, we'll just log the action
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'USER_SUSPENDED',
          resource: 'USER',
          details: {
            targetUserId: input.userId,
            reason: input.reason,
          },
        },
      })

      return { success: true }
    }),

  // Delete user (admin only)
  deleteUser: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        reason: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Prevent self-deletion
      if (ctx.session.user.id === input.userId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Cannot delete your own account',
        })
      }

      // Log the action before deletion
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'USER_DELETED',
          resource: 'USER',
          details: {
            targetUserId: input.userId,
            reason: input.reason,
          },
        },
      })

      await ctx.db.user.delete({
        where: { id: input.userId },
      })

      return { success: true }
    }),

  // Get user activity feed
  getActivityFeed: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(50).default(20),
        offset: z.number().min(0).default(0),
      })
    )
    .query(async ({ ctx, input }) => {
      const activities = await ctx.db.auditLog.findMany({
        where: { userId: ctx.session.user.id },
        orderBy: { createdAt: 'desc' },
        take: input.limit,
        skip: input.offset,
      })

      return activities
    }),

  // Get user preferences
  getPreferences: protectedProcedure.query(async ({ ctx }) => {
    // In a real app, you'd have a separate preferences table
    // For now, return default preferences
    return {
      theme: 'system',
      language: 'en',
      notifications: {
        email: true,
        push: false,
        marketing: false,
      },
      privacy: {
        profileVisible: false,
        activityVisible: false,
      },
      ai: {
        defaultProvider: 'OPENAI',
        defaultModel: 'gpt-4-turbo-preview',
        defaultTemperature: 0.7,
        defaultMaxTokens: 2000,
      },
    }
  }),

  // Update user preferences
  updatePreferences: protectedProcedure
    .input(
      z.object({
        theme: z.enum(['light', 'dark', 'system']).optional(),
        language: z.string().optional(),
        notifications: z
          .object({
            email: z.boolean().optional(),
            push: z.boolean().optional(),
            marketing: z.boolean().optional(),
          })
          .optional(),
        privacy: z
          .object({
            profileVisible: z.boolean().optional(),
            activityVisible: z.boolean().optional(),
          })
          .optional(),
        ai: z
          .object({
            defaultProvider: z.enum(['OPENAI', 'ANTHROPIC', 'GOOGLE', 'LOCAL', 'CUSTOM']).optional(),
            defaultModel: z.string().optional(),
            defaultTemperature: z.number().min(0).max(2).optional(),
            defaultMaxTokens: z.number().min(1).max(8000).optional(),
          })
          .optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Log the preference update
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'PREFERENCES_UPDATED',
          resource: 'USER',
          details: {
            preferences: input,
          },
        },
      })

      return { success: true }
    }),
})
