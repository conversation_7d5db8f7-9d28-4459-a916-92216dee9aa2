import { getServerSession, type NextAuthOptions, type DefaultSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module 'next-auth' {
  interface Session extends DefaultSession {
    user: {
      id: string
      role: string
      plan: string
      credits: number
    } & DefaultSession['user']
  }
}

/**
 * Wrapper for `getServerSession` so that you don't need to import the `authOptions` in every file.
 *
 * @see https://next-auth.js.org/configuration/nextjs
 */
export const getServerAuthSession = (ctx: {
  req: any
  res: any
}) => {
  return getServerSession(ctx.req, ctx.res, authOptions)
}
