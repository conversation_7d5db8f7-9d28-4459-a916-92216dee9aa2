import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/toaster'
import { TRPCProvider } from '@/components/providers/trpc-provider'
import { AuthProvider } from '@/components/providers/auth-provider'
import './globals.css'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: 'NexusAI - Next-Generation AI Chat Interface',
  description: 'Advanced AI chat platform with multi-provider support, conversation branching, and enterprise features',
  keywords: ['AI', 'chat', 'OpenAI', 'Anthropic', 'conversation', 'enterprise'],
  authors: [{ name: '<PERSON><PERSON>a1989' }],
  creator: 'HectorTa1989',
  publisher: 'NexusAI',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://nexusai.com'),
  openGraph: {
    title: 'NexusAI - Next-Generation AI Chat Interface',
    description: 'Advanced AI chat platform with multi-provider support, conversation branching, and enterprise features',
    url: 'https://nexusai.com',
    siteName: 'NexusAI',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'NexusAI Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'NexusAI - Next-Generation AI Chat Interface',
    description: 'Advanced AI chat platform with multi-provider support, conversation branching, and enterprise features',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <TRPCProvider>
              <div className="relative flex min-h-screen flex-col">
                <div className="flex-1">
                  {children}
                </div>
              </div>
              <Toaster />
            </TRPCProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
