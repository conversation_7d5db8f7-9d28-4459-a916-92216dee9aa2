import { NextAuthOptions } from 'next-auth'
import { PrismaAdapter } from '@auth/prisma-adapter'
import GoogleProvider from 'next-auth/providers/google'
import GitHubProvider from 'next-auth/providers/github'
import CredentialsProvider from 'next-auth/providers/credentials'
import { db } from '@/lib/db'
import { compare } from 'bcryptjs'
import type { User, UserRole } from '@prisma/client'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      name?: string | null
      image?: string | null
      role: UserRole
      plan: string
      credits: number
    }
  }

  interface User {
    role: UserRole
    plan: string
    credits: number
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole
    plan: string
    credits: number
  }
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db) as any,
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: '/login',
    signUp: '/register',
    error: '/auth/error',
  },
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Invalid credentials')
        }

        const user = await db.user.findUnique({
          where: {
            email: credentials.email,
          },
        })

        if (!user) {
          throw new Error('Invalid credentials')
        }

        // For OAuth users, password might not exist
        if (!user.password) {
          throw new Error('Please sign in with your OAuth provider')
        }

        const isPasswordValid = await compare(credentials.password, user.password)

        if (!isPasswordValid) {
          throw new Error('Invalid credentials')
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
          role: user.role,
          plan: user.plan,
          credits: user.credits,
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (user) {
        token.role = user.role
        token.plan = user.plan
        token.credits = user.credits
      }

      // Handle session updates
      if (trigger === 'update' && session) {
        token.name = session.name
        token.email = session.email
      }

      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role
        session.user.plan = token.plan
        session.user.credits = token.credits
      }

      return session
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google' || account?.provider === 'github') {
        try {
          // Check if user exists
          const existingUser = await db.user.findUnique({
            where: { email: user.email! },
          })

          if (!existingUser) {
            // Create new user with default values
            await db.user.create({
              data: {
                email: user.email!,
                name: user.name,
                image: user.image,
                role: 'USER',
                plan: 'FREE',
                credits: 100,
              },
            })
          }

          return true
        } catch (error) {
          console.error('Error during sign in:', error)
          return false
        }
      }

      return true
    },
  },
  events: {
    async signIn({ user, account, isNewUser }) {
      if (isNewUser) {
        // Log new user registration
        await db.auditLog.create({
          data: {
            userId: user.id,
            action: 'USER_REGISTERED',
            resource: 'USER',
            details: {
              provider: account?.provider,
              email: user.email,
            },
          },
        })
      }

      // Log sign in
      await db.auditLog.create({
        data: {
          userId: user.id,
          action: 'USER_SIGNED_IN',
          resource: 'USER',
          details: {
            provider: account?.provider,
          },
        },
      })
    },
    async signOut({ token }) {
      if (token?.sub) {
        // Log sign out
        await db.auditLog.create({
          data: {
            userId: token.sub,
            action: 'USER_SIGNED_OUT',
            resource: 'USER',
            details: {},
          },
        })
      }
    },
  },
  debug: process.env.NODE_ENV === 'development',
}

// Helper functions for role-based access control
export const hasRole = (userRole: UserRole, requiredRole: UserRole): boolean => {
  const roleHierarchy = {
    USER: 0,
    ADMIN: 1,
    SUPER_ADMIN: 2,
  }

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole]
}

export const canAccessResource = (
  userRole: UserRole,
  resourceOwner: string,
  userId: string,
  requiredRole: UserRole = 'USER'
): boolean => {
  // Users can access their own resources
  if (resourceOwner === userId) {
    return true
  }

  // Check if user has required role for accessing others' resources
  return hasRole(userRole, requiredRole)
}

// Rate limiting helper
export const checkRateLimit = async (userId: string, action: string): Promise<boolean> => {
  // Implement rate limiting logic here
  // This is a placeholder - you would typically use Redis for this
  return true
}
