import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure, protectedRateLimitedProcedure } from '@/server/api/trpc'
import { OpenAIProvider } from '@/lib/ai-providers/openai'
import { dbUtils } from '@/lib/db'

export const aiRouter = createTRPCRouter({
  // Get available AI providers and models
  getProviders: protectedProcedure.query(async ({ ctx }) => {
    const providers = []

    // OpenAI
    if (process.env.OPENAI_API_KEY) {
      const openai = new OpenAIProvider()
      try {
        const models = await openai.getModels()
        providers.push({
          id: 'openai',
          name: 'OpenAI',
          icon: '🤖',
          models,
          capabilities: {
            streaming: true,
            images: true,
            functions: true,
          },
          status: 'available',
        })
      } catch (error) {
        providers.push({
          id: 'openai',
          name: 'OpenAI',
          icon: '🤖',
          models: [],
          capabilities: {
            streaming: true,
            images: true,
            functions: true,
          },
          status: 'error',
          error: 'API key invalid or service unavailable',
        })
      }
    }

    // Add other providers here (Anthropic, Google, etc.)
    // For now, we'll add them as placeholder
    providers.push(
      {
        id: 'anthropic',
        name: 'Anthropic',
        icon: '🧠',
        models: [
          {
            id: 'claude-3-opus-20240229',
            name: 'Claude 3 Opus',
            provider: 'anthropic',
            contextLength: 200000,
            pricing: { input: 0.015, output: 0.075 },
          },
          {
            id: 'claude-3-sonnet-20240229',
            name: 'Claude 3 Sonnet',
            provider: 'anthropic',
            contextLength: 200000,
            pricing: { input: 0.003, output: 0.015 },
          },
        ],
        capabilities: {
          streaming: true,
          images: true,
          functions: false,
        },
        status: process.env.ANTHROPIC_API_KEY ? 'available' : 'unavailable',
      },
      {
        id: 'google',
        name: 'Google AI',
        icon: '🔍',
        models: [
          {
            id: 'gemini-pro',
            name: 'Gemini Pro',
            provider: 'google',
            contextLength: 32768,
            pricing: { input: 0.0005, output: 0.0015 },
          },
          {
            id: 'gemini-pro-vision',
            name: 'Gemini Pro Vision',
            provider: 'google',
            contextLength: 16384,
            pricing: { input: 0.00025, output: 0.0005 },
          },
        ],
        capabilities: {
          streaming: true,
          images: true,
          functions: true,
        },
        status: process.env.GOOGLE_AI_API_KEY ? 'available' : 'unavailable',
      }
    )

    return providers
  }),

  // Send a chat message to AI
  chat: protectedRateLimitedProcedure
    .input(
      z.object({
        conversationId: z.string(),
        message: z.string().min(1),
        provider: z.enum(['OPENAI', 'ANTHROPIC', 'GOOGLE', 'LOCAL', 'CUSTOM']),
        model: z.string(),
        options: z
          .object({
            temperature: z.number().min(0).max(2).optional(),
            maxTokens: z.number().min(1).max(8000).optional(),
            stream: z.boolean().optional(),
          })
          .optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Verify conversation ownership
      const conversation = await ctx.db.conversation.findFirst({
        where: {
          id: input.conversationId,
          userId: ctx.session.user.id,
        },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
          },
        },
      })

      if (!conversation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Conversation not found',
        })
      }

      // Check user credits
      if (ctx.session.user.credits <= 0) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient credits',
        })
      }

      // Add user message to conversation
      const userMessage = await ctx.db.message.create({
        data: {
          conversationId: input.conversationId,
          role: 'USER',
          content: input.message,
        },
      })

      try {
        // Prepare messages for AI
        const messages = [
          ...conversation.messages.map((msg) => ({
            role: msg.role.toLowerCase() as 'system' | 'user' | 'assistant',
            content: msg.content,
          })),
          {
            role: 'user' as const,
            content: input.message,
          },
        ]

        // Get AI provider
        let aiProvider
        switch (input.provider) {
          case 'OPENAI':
            aiProvider = new OpenAIProvider()
            break
          default:
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: 'Provider not supported yet',
            })
        }

        // Send to AI
        const response = await aiProvider.chat(messages, {
          model: input.model,
          temperature: input.options?.temperature || 0.7,
          maxTokens: input.options?.maxTokens || 2000,
          stream: input.options?.stream || false,
        })

        // Calculate cost
        const cost = aiProvider.calculateCost(response.usage, input.model)

        // Save AI response
        const aiMessage = await ctx.db.message.create({
          data: {
            conversationId: input.conversationId,
            role: 'ASSISTANT',
            content: response.content,
            metadata: {
              provider: input.provider,
              model: input.model,
              usage: response.usage,
              cost,
              finishReason: response.finishReason,
            },
          },
        })

        // Track usage
        await dbUtils.trackUsage(
          ctx.session.user.id,
          input.provider,
          input.model,
          response.usage.totalTokens,
          cost
        )

        // Update conversation timestamp
        await ctx.db.conversation.update({
          where: { id: input.conversationId },
          data: { updatedAt: new Date() },
        })

        return {
          userMessage,
          aiMessage,
          usage: response.usage,
          cost,
        }
      } catch (error) {
        // Log error but don't expose internal details
        console.error('AI chat error:', error)
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get AI response. Please try again.',
        })
      }
    }),

  // Get model information
  getModelInfo: protectedProcedure
    .input(
      z.object({
        provider: z.enum(['OPENAI', 'ANTHROPIC', 'GOOGLE', 'LOCAL', 'CUSTOM']),
        model: z.string(),
      })
    )
    .query(async ({ input }) => {
      let aiProvider
      switch (input.provider) {
        case 'OPENAI':
          aiProvider = new OpenAIProvider()
          break
        default:
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Provider not supported yet',
          })
      }

      const models = await aiProvider.getModels()
      const modelInfo = models.find((m) => m.id === input.model)

      if (!modelInfo) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Model not found',
        })
      }

      return modelInfo
    }),

  // Estimate tokens for text
  estimateTokens: protectedProcedure
    .input(
      z.object({
        text: z.string(),
        provider: z.enum(['OPENAI', 'ANTHROPIC', 'GOOGLE', 'LOCAL', 'CUSTOM']).default('OPENAI'),
      })
    )
    .query(async ({ input }) => {
      let aiProvider
      switch (input.provider) {
        case 'OPENAI':
          aiProvider = new OpenAIProvider()
          break
        default:
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Provider not supported yet',
          })
      }

      const tokens = await aiProvider.estimateTokens(input.text)
      return { tokens, characters: input.text.length }
    }),

  // Moderate content
  moderateContent: protectedProcedure
    .input(z.object({ content: z.string() }))
    .mutation(async ({ input }) => {
      const openai = new OpenAIProvider()
      
      try {
        const moderation = await openai.moderateContent(input.content)
        return moderation
      } catch (error) {
        console.error('Content moderation error:', error)
        return {
          flagged: false,
          categories: [],
          scores: {},
        }
      }
    }),

  // Get AI usage analytics
  getUsageAnalytics: protectedProcedure
    .input(
      z.object({
        days: z.number().min(1).max(365).default(30),
      })
    )
    .query(async ({ ctx, input }) => {
      const analytics = await dbUtils.getUsageAnalytics(ctx.session.user.id, input.days)
      
      // Process analytics data
      const totalTokens = analytics.reduce((sum, stat) => sum + stat.tokensUsed, 0)
      const totalCost = analytics.reduce((sum, stat) => sum + stat.cost, 0)
      const totalMessages = analytics.reduce((sum, stat) => sum + stat.messagesCount, 0)

      const providerBreakdown = analytics.reduce((acc, stat) => {
        if (!acc[stat.provider]) {
          acc[stat.provider] = {
            tokens: 0,
            cost: 0,
            messages: 0,
          }
        }
        acc[stat.provider].tokens += stat.tokensUsed
        acc[stat.provider].cost += stat.cost
        acc[stat.provider].messages += stat.messagesCount
        return acc
      }, {} as Record<string, { tokens: number; cost: number; messages: number }>)

      const modelBreakdown = analytics.reduce((acc, stat) => {
        if (!acc[stat.model]) {
          acc[stat.model] = {
            tokens: 0,
            cost: 0,
            messages: 0,
          }
        }
        acc[stat.model].tokens += stat.tokensUsed
        acc[stat.model].cost += stat.cost
        acc[stat.model].messages += stat.messagesCount
        return acc
      }, {} as Record<string, { tokens: number; cost: number; messages: number }>)

      return {
        summary: {
          totalTokens,
          totalCost,
          totalMessages,
          averageCostPerMessage: totalMessages > 0 ? totalCost / totalMessages : 0,
        },
        providerBreakdown,
        modelBreakdown,
        dailyStats: analytics,
      }
    }),
})
