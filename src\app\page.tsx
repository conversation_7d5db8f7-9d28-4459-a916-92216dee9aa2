import { Suspense } from 'react'
import Link from 'next/link'
import { ArrowRight, Bot, Zap, Shield, Users, BarChart3, Workflow } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { HeroSection } from '@/components/landing/hero-section'
import { FeatureGrid } from '@/components/landing/feature-grid'
import { PricingSection } from '@/components/landing/pricing-section'
import { TestimonialsSection } from '@/components/landing/testimonials-section'

export default function HomePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Navigation */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex">
            <Link href="/" className="mr-6 flex items-center space-x-2">
              <Bot className="h-6 w-6" />
              <span className="font-bold">NexusAI</span>
            </Link>
          </div>
          <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link href="#features" className="transition-colors hover:text-foreground/80">
                Features
              </Link>
              <Link href="#pricing" className="transition-colors hover:text-foreground/80">
                Pricing
              </Link>
              <Link href="/docs" className="transition-colors hover:text-foreground/80">
                Docs
              </Link>
            </nav>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/login">Sign In</Link>
              </Button>
              <Button size="sm" asChild>
                <Link href="/register">Get Started</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-1">
        {/* Hero Section */}
        <Suspense fallback={<div className="h-96 animate-pulse bg-muted" />}>
          <HeroSection />
        </Suspense>

        {/* Features Section */}
        <section id="features" className="py-24 bg-muted/50">
          <div className="container">
            <div className="text-center mb-16">
              <Badge variant="outline" className="mb-4">
                Features
              </Badge>
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Everything you need for AI conversations
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Advanced features that go beyond simple chat interfaces, designed for professionals and enterprises.
              </p>
            </div>
            
            <Suspense fallback={<div className="h-96 animate-pulse bg-muted" />}>
              <FeatureGrid />
            </Suspense>
          </div>
        </section>

        {/* Key Features Highlight */}
        <section className="py-24">
          <div className="container">
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <Bot className="h-8 w-8 mb-2 text-primary" />
                  <CardTitle>Multi-Provider AI</CardTitle>
                  <CardDescription>
                    Connect to OpenAI, Anthropic, Google, and local models from one interface
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Intelligent model routing</li>
                    <li>• Cost optimization</li>
                    <li>• Fallback mechanisms</li>
                    <li>• Custom endpoints</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Workflow className="h-8 w-8 mb-2 text-primary" />
                  <CardTitle>Conversation Branching</CardTitle>
                  <CardDescription>
                    Create, merge, and compare different conversation paths
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Visual tree interface</li>
                    <li>• A/B testing prompts</li>
                    <li>• Parallel processing</li>
                    <li>• Smart merging</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Users className="h-8 w-8 mb-2 text-primary" />
                  <CardTitle>Real-time Collaboration</CardTitle>
                  <CardDescription>
                    Work together on conversations with role-based permissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Live editing</li>
                    <li>• Comment system</li>
                    <li>• Access controls</li>
                    <li>• Audit trails</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Zap className="h-8 w-8 mb-2 text-primary" />
                  <CardTitle>Advanced Prompting</CardTitle>
                  <CardDescription>
                    Professional prompt engineering tools with optimization
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Template library</li>
                    <li>• Variable injection</li>
                    <li>• Performance analytics</li>
                    <li>• AI suggestions</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <BarChart3 className="h-8 w-8 mb-2 text-primary" />
                  <CardTitle>Analytics & Insights</CardTitle>
                  <CardDescription>
                    Comprehensive analytics for usage, costs, and performance
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Usage tracking</li>
                    <li>• Cost analysis</li>
                    <li>• Model comparison</li>
                    <li>• Export reports</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Shield className="h-8 w-8 mb-2 text-primary" />
                  <CardTitle>Enterprise Security</CardTitle>
                  <CardDescription>
                    Enterprise-grade security with SSO and compliance features
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• SSO integration</li>
                    <li>• Data encryption</li>
                    <li>• Compliance ready</li>
                    <li>• Audit logging</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-24 bg-muted/50">
          <div className="container">
            <div className="text-center mb-16">
              <Badge variant="outline" className="mb-4">
                Pricing
              </Badge>
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Choose your plan
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Flexible pricing for individuals, teams, and enterprises.
              </p>
            </div>
            
            <Suspense fallback={<div className="h-96 animate-pulse bg-muted" />}>
              <PricingSection />
            </Suspense>
          </div>
        </section>

        {/* Testimonials */}
        <section className="py-24">
          <div className="container">
            <div className="text-center mb-16">
              <Badge variant="outline" className="mb-4">
                Testimonials
              </Badge>
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Loved by professionals
              </h2>
            </div>
            
            <Suspense fallback={<div className="h-96 animate-pulse bg-muted" />}>
              <TestimonialsSection />
            </Suspense>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-24 bg-primary text-primary-foreground">
          <div className="container text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              Ready to transform your AI conversations?
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Join thousands of professionals using NexusAI for advanced AI interactions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/register">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/demo">
                  Schedule Demo
                </Link>
              </Button>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t bg-background">
        <div className="container py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Bot className="h-5 w-5" />
              <span className="font-semibold">NexusAI</span>
            </div>
            <div className="flex space-x-6 text-sm text-muted-foreground">
              <Link href="/privacy" className="hover:text-foreground">Privacy</Link>
              <Link href="/terms" className="hover:text-foreground">Terms</Link>
              <Link href="/contact" className="hover:text-foreground">Contact</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
