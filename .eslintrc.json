{"extends": ["next/core-web-vitals", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/consistent-type-imports": ["warn", {"prefer": "type-imports", "fixStyle": "inline-type-imports"}], "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-non-null-assertion": "warn"}}