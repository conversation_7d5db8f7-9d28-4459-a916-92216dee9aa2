import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create admin user
  const adminPassword = await hash('admin123', 12)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'SUPER_ADMIN',
      plan: 'ENTERPRISE',
      credits: 10000,
      password: adminPassword,
    },
  })

  console.log('✅ Created admin user:', admin.email)

  // Create demo user
  const demoPassword = await hash('demo123', 12)
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Demo User',
      role: 'USER',
      plan: '<PERSON>O',
      credits: 1000,
      password: demoPassword,
    },
  })

  console.log('✅ Created demo user:', demoUser.email)

  // Create sample workspace
  const workspace = await prisma.workspace.create({
    data: {
      name: 'Demo Workspace',
      description: 'A sample workspace for demonstration purposes',
      settings: {
        theme: 'light',
        defaultModel: 'gpt-4-turbo-preview',
        defaultProvider: 'OPENAI',
      },
    },
  })

  // Add demo user to workspace
  await prisma.workspaceMember.create({
    data: {
      userId: demoUser.id,
      workspaceId: workspace.id,
      role: 'OWNER',
    },
  })

  console.log('✅ Created demo workspace:', workspace.name)

  // Create sample conversation
  const conversation = await prisma.conversation.create({
    data: {
      title: 'Getting Started with NexusAI',
      userId: demoUser.id,
      workspaceId: workspace.id,
      model: 'gpt-4-turbo-preview',
      provider: 'OPENAI',
      metadata: {
        temperature: 0.7,
        maxTokens: 2000,
      },
    },
  })

  // Add sample messages
  await prisma.message.createMany({
    data: [
      {
        conversationId: conversation.id,
        role: 'USER',
        content: 'Hello! Can you help me understand what NexusAI can do?',
      },
      {
        conversationId: conversation.id,
        role: 'ASSISTANT',
        content: `Hello! I'd be happy to help you understand NexusAI's capabilities. NexusAI is a next-generation AI chat interface that offers several advanced features:

🤖 **Multi-Provider Support**: Connect to OpenAI, Anthropic, Google, and local models
🌳 **Conversation Branching**: Create and compare different conversation paths
👥 **Real-time Collaboration**: Work with team members on conversations
🎯 **Advanced Prompting**: Professional prompt engineering tools
📊 **Analytics**: Track usage, costs, and performance
🔒 **Enterprise Security**: SSO, audit logging, and compliance features

Would you like me to elaborate on any of these features?`,
        metadata: {
          tokensUsed: 150,
          cost: 0.003,
          responseTime: 1200,
        },
      },
      {
        conversationId: conversation.id,
        role: 'USER',
        content: 'Tell me more about conversation branching. How does it work?',
      },
      {
        conversationId: conversation.id,
        role: 'ASSISTANT',
        content: `Conversation branching is one of NexusAI's most powerful features! Here's how it works:

🌳 **Visual Tree Interface**: See your conversation as a branching tree structure
🔀 **Create Branches**: At any point, create alternative conversation paths
⚡ **Parallel Processing**: Test different prompts or models simultaneously
🔍 **A/B Testing**: Compare responses to find the best approach
🔄 **Smart Merging**: Combine the best parts from different branches

**Example Use Cases:**
- Test different prompt variations to optimize results
- Explore multiple solution approaches to a problem
- Compare responses from different AI models
- Create "what-if" scenarios in your conversations

**How to Use:**
1. Right-click any message to create a branch
2. Give your branch a descriptive name
3. Continue the conversation in the new branch
4. Switch between branches to compare results
5. Merge successful branches back to the main conversation

This feature is especially powerful for prompt engineering, creative writing, and complex problem-solving!`,
        metadata: {
          tokensUsed: 220,
          cost: 0.0044,
          responseTime: 1800,
        },
      },
    ],
  })

  console.log('✅ Created sample conversation with messages')

  // Create sample prompts
  const prompts = [
    {
      title: 'Code Review Assistant',
      description: 'Helps review code for best practices, bugs, and improvements',
      content: `You are an expert code reviewer. Please review the following code and provide feedback on:

1. **Code Quality**: Structure, readability, and maintainability
2. **Best Practices**: Following language/framework conventions
3. **Potential Issues**: Bugs, security vulnerabilities, performance problems
4. **Improvements**: Suggestions for optimization and enhancement

Code to review:
{code}

Please provide constructive feedback with specific examples and suggestions.`,
      category: 'Development',
      tags: ['code-review', 'development', 'quality-assurance'],
      userId: demoUser.id,
      isPublic: true,
      isTemplate: true,
      status: 'PUBLISHED',
    },
    {
      title: 'Technical Documentation Writer',
      description: 'Creates comprehensive technical documentation',
      content: `You are a technical documentation specialist. Create clear, comprehensive documentation for:

**Topic**: {topic}
**Audience**: {audience}
**Format**: {format}

Please include:
- Clear overview and purpose
- Step-by-step instructions
- Code examples (if applicable)
- Common troubleshooting tips
- Best practices and recommendations

Make the documentation accessible to the target audience while being thorough and accurate.`,
      category: 'Documentation',
      tags: ['documentation', 'technical-writing', 'guides'],
      userId: demoUser.id,
      isPublic: true,
      isTemplate: true,
      status: 'PUBLISHED',
    },
    {
      title: 'Business Strategy Analyst',
      description: 'Analyzes business scenarios and provides strategic insights',
      content: `You are a senior business strategy consultant. Analyze the following business scenario and provide strategic insights:

**Company/Situation**: {company_info}
**Challenge/Opportunity**: {challenge}
**Industry Context**: {industry}

Please provide:
1. **Situation Analysis**: Current state assessment
2. **Key Challenges**: Primary obstacles and risks
3. **Opportunities**: Potential areas for growth/improvement
4. **Strategic Recommendations**: Actionable next steps
5. **Success Metrics**: How to measure progress

Base your analysis on proven business frameworks and industry best practices.`,
      category: 'Business',
      tags: ['strategy', 'analysis', 'consulting', 'business'],
      userId: demoUser.id,
      isPublic: true,
      isTemplate: true,
      status: 'PUBLISHED',
    },
  ]

  for (const promptData of prompts) {
    await prisma.prompt.create({ data: promptData })
  }

  console.log('✅ Created sample prompt templates')

  // Create sample usage stats
  const today = new Date()
  const usageStats = []

  for (let i = 0; i < 30; i++) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    date.setHours(0, 0, 0, 0)

    usageStats.push({
      userId: demoUser.id,
      date,
      provider: 'OPENAI',
      model: 'gpt-4-turbo-preview',
      tokensUsed: Math.floor(Math.random() * 5000) + 1000,
      messagesCount: Math.floor(Math.random() * 50) + 10,
      cost: Math.random() * 10 + 1,
    })

    if (Math.random() > 0.7) {
      usageStats.push({
        userId: demoUser.id,
        date,
        provider: 'ANTHROPIC',
        model: 'claude-3-opus',
        tokensUsed: Math.floor(Math.random() * 3000) + 500,
        messagesCount: Math.floor(Math.random() * 30) + 5,
        cost: Math.random() * 8 + 0.5,
      })
    }
  }

  await prisma.usageStats.createMany({
    data: usageStats,
  })

  console.log('✅ Created sample usage statistics')

  // Create audit logs
  await prisma.auditLog.createMany({
    data: [
      {
        userId: demoUser.id,
        action: 'USER_REGISTERED',
        resource: 'USER',
        details: { email: demoUser.email },
      },
      {
        userId: demoUser.id,
        action: 'CONVERSATION_CREATED',
        resource: 'CONVERSATION',
        details: { conversationId: conversation.id, title: conversation.title },
      },
      {
        userId: admin.id,
        action: 'USER_REGISTERED',
        resource: 'USER',
        details: { email: admin.email, role: 'SUPER_ADMIN' },
      },
    ],
  })

  console.log('✅ Created audit logs')

  console.log('🎉 Database seeding completed successfully!')
  console.log('\n📋 Demo Credentials:')
  console.log('Admin: <EMAIL> / admin123')
  console.log('Demo User: <EMAIL> / demo123')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
