// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  role          UserRole  @default(USER)
  plan          UserPlan  @default(FREE)
  credits       Int       @default(100)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]
  sessions      Session[]
  conversations Conversation[]
  prompts       Prompt[]
  workspaces    WorkspaceMember[]
  auditLogs     AuditLog[]
  usageStats    UsageStats[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Core application models
model Conversation {
  id          String            @id @default(cuid())
  title       String
  userId      String
  workspaceId String?
  model       String
  provider    AIProvider
  status      ConversationStatus @default(ACTIVE)
  parentId    String?           // For conversation branching
  branchName  String?
  metadata    Json?             // Store model parameters, context, etc.
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace Workspace? @relation(fields: [workspaceId], references: [id])
  parent    Conversation? @relation("ConversationBranches", fields: [parentId], references: [id])
  branches  Conversation[] @relation("ConversationBranches")
  messages  Message[]
  exports   Export[]

  @@map("conversations")
}

model Message {
  id             String      @id @default(cuid())
  conversationId String
  role           MessageRole
  content        String      @db.Text
  metadata       Json?       // Store tokens, cost, processing time, etc.
  parentId       String?     // For message branching
  isEdited       Boolean     @default(false)
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  parent       Message?     @relation("MessageBranches", fields: [parentId], references: [id])
  branches     Message[]    @relation("MessageBranches")

  @@map("messages")
}

model Prompt {
  id          String       @id @default(cuid())
  title       String
  description String?
  content     String       @db.Text
  category    String?
  tags        String[]
  userId      String
  isPublic    Boolean      @default(false)
  isTemplate  Boolean      @default(false)
  version     Int          @default(1)
  status      PromptStatus @default(DRAFT)
  metadata    Json?        // A/B testing results, performance metrics
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("prompts")
}

model Workspace {
  id          String    @id @default(cuid())
  name        String
  description String?
  settings    Json?     // Workspace-specific settings
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  members       WorkspaceMember[]
  conversations Conversation[]

  @@map("workspaces")
}

model WorkspaceMember {
  id          String        @id @default(cuid())
  userId      String
  workspaceId String
  role        WorkspaceRole @default(MEMBER)
  joinedAt    DateTime      @default(now())

  // Relations
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@unique([userId, workspaceId])
  @@map("workspace_members")
}

model Export {
  id             String     @id @default(cuid())
  conversationId String
  format         ExportFormat
  destination    String     // URL or service identifier
  status         ExportStatus @default(PENDING)
  metadata       Json?
  createdAt      DateTime   @default(now())
  completedAt    DateTime?

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@map("exports")
}

model UsageStats {
  id           String   @id @default(cuid())
  userId       String
  date         DateTime @db.Date
  tokensUsed   Int      @default(0)
  messagesCount Int     @default(0)
  cost         Float    @default(0)
  provider     AIProvider
  model        String

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date, provider, model])
  @@map("usage_stats")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  resource  String
  details   Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("audit_logs")
}

// Enums
enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

enum UserPlan {
  FREE
  PRO
  ENTERPRISE
}

enum AIProvider {
  OPENAI
  ANTHROPIC
  GOOGLE
  LOCAL
  CUSTOM
}

enum ConversationStatus {
  ACTIVE
  ARCHIVED
  DELETED
}

enum MessageRole {
  USER
  ASSISTANT
  SYSTEM
}

enum PromptStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum WorkspaceRole {
  OWNER
  ADMIN
  MEMBER
  VIEWER
}

enum ExportFormat {
  JSON
  MARKDOWN
  PDF
  NOTION
  OBSIDIAN
  GOOGLE_DOCS
}

enum ExportStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}
