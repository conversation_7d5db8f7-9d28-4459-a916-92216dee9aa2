import { z } from 'zod'
import { hash } from 'bcryptjs'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, publicProcedure, protectedProcedure } from '@/server/api/trpc'

export const authRouter = createTRPCRouter({
  // Register new user
  register: publicProcedure
    .input(
      z.object({
        name: z.string().min(2).max(50),
        email: z.string().email(),
        password: z.string().min(8).max(100),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check if user already exists
      const existingUser = await ctx.db.user.findUnique({
        where: { email: input.email },
      })

      if (existingUser) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'User with this email already exists',
        })
      }

      // Hash password
      const hashedPassword = await hash(input.password, 12)

      // Create user
      const user = await ctx.db.user.create({
        data: {
          name: input.name,
          email: input.email,
          password: hashedPassword,
          role: 'USER',
          plan: 'FREE',
          credits: 100,
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          plan: true,
          credits: true,
          createdAt: true,
        },
      })

      // Log registration
      await ctx.db.auditLog.create({
        data: {
          userId: user.id,
          action: 'USER_REGISTERED',
          resource: 'USER',
          details: {
            email: user.email,
            registrationMethod: 'credentials',
          },
        },
      })

      return user
    }),

  // Get current user profile
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.db.user.findUnique({
      where: { id: ctx.session.user.id },
      include: {
        _count: {
          select: {
            conversations: true,
            prompts: true,
            workspaces: true,
          },
        },
        usageStats: {
          where: {
            date: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
          orderBy: { date: 'desc' },
        },
      },
    })

    if (!user) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'User not found',
      })
    }

    return user
  }),

  // Update user profile
  updateProfile: protectedProcedure
    .input(
      z.object({
        name: z.string().min(2).max(50).optional(),
        image: z.string().url().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: {
          name: input.name,
          image: input.image,
          updatedAt: new Date(),
        },
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          role: true,
          plan: true,
          credits: true,
          updatedAt: true,
        },
      })

      // Log profile update
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'PROFILE_UPDATED',
          resource: 'USER',
          details: {
            changes: input,
          },
        },
      })

      return user
    }),

  // Change password
  changePassword: protectedProcedure
    .input(
      z.object({
        currentPassword: z.string(),
        newPassword: z.string().min(8).max(100),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
      })

      if (!user?.password) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot change password for OAuth users',
        })
      }

      // Verify current password
      const { compare } = await import('bcryptjs')
      const isValidPassword = await compare(input.currentPassword, user.password)

      if (!isValidPassword) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Current password is incorrect',
        })
      }

      // Hash new password
      const hashedPassword = await hash(input.newPassword, 12)

      // Update password
      await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: {
          password: hashedPassword,
          updatedAt: new Date(),
        },
      })

      // Log password change
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'PASSWORD_CHANGED',
          resource: 'USER',
          details: {},
        },
      })

      return { success: true }
    }),

  // Delete account
  deleteAccount: protectedProcedure
    .input(
      z.object({
        password: z.string().optional(),
        confirmation: z.literal('DELETE_MY_ACCOUNT'),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      // Verify password for credential users
      if (user.password && input.password) {
        const { compare } = await import('bcryptjs')
        const isValidPassword = await compare(input.password, user.password)

        if (!isValidPassword) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: 'Password is incorrect',
          })
        }
      }

      // Log account deletion before deleting
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'ACCOUNT_DELETED',
          resource: 'USER',
          details: {
            email: user.email,
            deletedAt: new Date(),
          },
        },
      })

      // Delete user (cascade will handle related records)
      await ctx.db.user.delete({
        where: { id: ctx.session.user.id },
      })

      return { success: true }
    }),

  // Get user settings
  getSettings: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.db.user.findUnique({
      where: { id: ctx.session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        role: true,
        plan: true,
        credits: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    if (!user) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'User not found',
      })
    }

    return {
      user,
      preferences: {
        theme: 'system',
        language: 'en',
        notifications: {
          email: true,
          push: false,
          marketing: false,
        },
        privacy: {
          profileVisible: false,
          activityVisible: false,
        },
      },
    }
  }),

  // Update user settings
  updateSettings: protectedProcedure
    .input(
      z.object({
        preferences: z
          .object({
            theme: z.enum(['light', 'dark', 'system']).optional(),
            language: z.string().optional(),
            notifications: z
              .object({
                email: z.boolean().optional(),
                push: z.boolean().optional(),
                marketing: z.boolean().optional(),
              })
              .optional(),
            privacy: z
              .object({
                profileVisible: z.boolean().optional(),
                activityVisible: z.boolean().optional(),
              })
              .optional(),
          })
          .optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // In a real app, you'd store preferences in a separate table
      // For now, we'll just log the update
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'SETTINGS_UPDATED',
          resource: 'USER',
          details: {
            preferences: input.preferences,
          },
        },
      })

      return { success: true }
    }),

  // Get audit logs for current user
  getAuditLogs: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      })
    )
    .query(async ({ ctx, input }) => {
      const logs = await ctx.db.auditLog.findMany({
        where: { userId: ctx.session.user.id },
        orderBy: { createdAt: 'desc' },
        take: input.limit,
        skip: input.offset,
      })

      const total = await ctx.db.auditLog.count({
        where: { userId: ctx.session.user.id },
      })

      return {
        logs,
        total,
        hasMore: input.offset + input.limit < total,
      }
    }),
})
