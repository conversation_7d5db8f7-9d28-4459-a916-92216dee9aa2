import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const db =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
  })

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db

// Database utility functions
export const dbUtils = {
  // User utilities
  async getUserWithStats(userId: string) {
    return await db.user.findUnique({
      where: { id: userId },
      include: {
        usageStats: {
          orderBy: { date: 'desc' },
          take: 30, // Last 30 days
        },
        conversations: {
          where: { status: 'ACTIVE' },
          orderBy: { updatedAt: 'desc' },
          take: 10,
        },
        _count: {
          select: {
            conversations: true,
            prompts: true,
          },
        },
      },
    })
  },

  // Conversation utilities
  async getConversationWithMessages(conversationId: string, userId: string) {
    return await db.conversation.findFirst({
      where: {
        id: conversationId,
        userId: userId,
      },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' },
        },
        branches: {
          include: {
            messages: {
              orderBy: { createdAt: 'asc' },
            },
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    })
  },

  async createConversationBranch(parentId: string, branchName: string, userId: string) {
    const parent = await db.conversation.findFirst({
      where: { id: parentId, userId },
      include: { messages: true },
    })

    if (!parent) {
      throw new Error('Parent conversation not found')
    }

    // Create new conversation branch
    const branch = await db.conversation.create({
      data: {
        title: `${parent.title} - ${branchName}`,
        userId: parent.userId,
        workspaceId: parent.workspaceId,
        model: parent.model,
        provider: parent.provider,
        parentId: parent.id,
        branchName,
        metadata: parent.metadata,
      },
    })

    // Copy messages to the new branch
    for (const message of parent.messages) {
      await db.message.create({
        data: {
          conversationId: branch.id,
          role: message.role,
          content: message.content,
          metadata: message.metadata,
        },
      })
    }

    return branch
  },

  // Usage tracking utilities
  async trackUsage(userId: string, provider: string, model: string, tokensUsed: number, cost: number) {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    await db.usageStats.upsert({
      where: {
        userId_date_provider_model: {
          userId,
          date: today,
          provider: provider as any,
          model,
        },
      },
      update: {
        tokensUsed: { increment: tokensUsed },
        messagesCount: { increment: 1 },
        cost: { increment: cost },
      },
      create: {
        userId,
        date: today,
        provider: provider as any,
        model,
        tokensUsed,
        messagesCount: 1,
        cost,
      },
    })

    // Update user credits
    await db.user.update({
      where: { id: userId },
      data: {
        credits: { decrement: Math.ceil(cost * 100) }, // Convert to credits
      },
    })
  },

  // Workspace utilities
  async getUserWorkspaces(userId: string) {
    return await db.workspaceMember.findMany({
      where: { userId },
      include: {
        workspace: {
          include: {
            _count: {
              select: {
                members: true,
                conversations: true,
              },
            },
          },
        },
      },
      orderBy: {
        workspace: {
          name: 'asc',
        },
      },
    })
  },

  async createWorkspace(name: string, description: string, ownerId: string) {
    return await db.$transaction(async (tx) => {
      const workspace = await tx.workspace.create({
        data: {
          name,
          description,
        },
      })

      await tx.workspaceMember.create({
        data: {
          userId: ownerId,
          workspaceId: workspace.id,
          role: 'OWNER',
        },
      })

      return workspace
    })
  },

  // Prompt utilities
  async getPublicPrompts(limit: number = 20, offset: number = 0) {
    return await db.prompt.findMany({
      where: {
        isPublic: true,
        status: 'PUBLISHED',
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
    })
  },

  // Analytics utilities
  async getUsageAnalytics(userId: string, days: number = 30) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    return await db.usageStats.findMany({
      where: {
        userId,
        date: {
          gte: startDate,
        },
      },
      orderBy: { date: 'asc' },
    })
  },

  async getSystemAnalytics(days: number = 30) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const [totalUsers, totalConversations, totalMessages, usageStats] = await Promise.all([
      db.user.count(),
      db.conversation.count({
        where: {
          createdAt: {
            gte: startDate,
          },
        },
      }),
      db.message.count({
        where: {
          createdAt: {
            gte: startDate,
          },
        },
      }),
      db.usageStats.groupBy({
        by: ['provider', 'date'],
        where: {
          date: {
            gte: startDate,
          },
        },
        _sum: {
          tokensUsed: true,
          messagesCount: true,
          cost: true,
        },
      }),
    ])

    return {
      totalUsers,
      totalConversations,
      totalMessages,
      usageStats,
    }
  },

  // Cleanup utilities
  async cleanupOldSessions() {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    await db.session.deleteMany({
      where: {
        expires: {
          lt: thirtyDaysAgo,
        },
      },
    })
  },

  async archiveOldConversations(userId: string, days: number = 90) {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - days)

    await db.conversation.updateMany({
      where: {
        userId,
        updatedAt: {
          lt: cutoffDate,
        },
        status: 'ACTIVE',
      },
      data: {
        status: 'ARCHIVED',
      },
    })
  },
}
