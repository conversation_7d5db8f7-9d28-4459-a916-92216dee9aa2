import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure } from '@/server/api/trpc'
import { dbUtils } from '@/lib/db'

export const workspaceRouter = createTRPCRouter({
  // Get user's workspaces
  getUserWorkspaces: protectedProcedure.query(async ({ ctx }) => {
    const workspaces = await dbUtils.getUserWorkspaces(ctx.session.user.id)
    return workspaces
  }),

  // Get single workspace
  getWorkspace: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const workspace = await ctx.db.workspaceMember.findFirst({
        where: {
          workspaceId: input.id,
          userId: ctx.session.user.id,
        },
        include: {
          workspace: {
            include: {
              members: {
                include: {
                  user: {
                    select: {
                      id: true,
                      name: true,
                      email: true,
                      image: true,
                    },
                  },
                },
              },
              conversations: {
                where: { status: 'ACTIVE' },
                orderBy: { updatedAt: 'desc' },
                take: 10,
                include: {
                  messages: {
                    take: 1,
                    orderBy: { createdAt: 'desc' },
                  },
                  _count: {
                    select: { messages: true },
                  },
                },
              },
              _count: {
                select: {
                  members: true,
                  conversations: true,
                },
              },
            },
          },
        },
      })

      if (!workspace) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Workspace not found',
        })
      }

      return workspace
    }),

  // Create workspace
  createWorkspace: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1).max(100),
        description: z.string().max(500).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const workspace = await dbUtils.createWorkspace(
        input.name,
        input.description || '',
        ctx.session.user.id
      )

      // Log the action
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'WORKSPACE_CREATED',
          resource: 'WORKSPACE',
          details: {
            workspaceId: workspace.id,
            name: workspace.name,
          },
        },
      })

      return workspace
    }),

  // Update workspace
  updateWorkspace: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).max(100).optional(),
        description: z.string().max(500).optional(),
        settings: z.record(z.any()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check if user is owner or admin
      const membership = await ctx.db.workspaceMember.findFirst({
        where: {
          workspaceId: input.id,
          userId: ctx.session.user.id,
          role: { in: ['OWNER', 'ADMIN'] },
        },
      })

      if (!membership) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient permissions',
        })
      }

      const { id, ...updateData } = input
      const workspace = await ctx.db.workspace.update({
        where: { id: input.id },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
      })

      return workspace
    }),

  // Delete workspace
  deleteWorkspace: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if user is owner
      const membership = await ctx.db.workspaceMember.findFirst({
        where: {
          workspaceId: input.id,
          userId: ctx.session.user.id,
          role: 'OWNER',
        },
      })

      if (!membership) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only workspace owners can delete workspaces',
        })
      }

      await ctx.db.workspace.delete({
        where: { id: input.id },
      })

      return { success: true }
    }),

  // Invite user to workspace
  inviteUser: protectedProcedure
    .input(
      z.object({
        workspaceId: z.string(),
        email: z.string().email(),
        role: z.enum(['MEMBER', 'ADMIN']).default('MEMBER'),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check permissions
      const membership = await ctx.db.workspaceMember.findFirst({
        where: {
          workspaceId: input.workspaceId,
          userId: ctx.session.user.id,
          role: { in: ['OWNER', 'ADMIN'] },
        },
      })

      if (!membership) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient permissions',
        })
      }

      // Find user by email
      const user = await ctx.db.user.findUnique({
        where: { email: input.email },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      // Check if user is already a member
      const existingMembership = await ctx.db.workspaceMember.findUnique({
        where: {
          userId_workspaceId: {
            userId: user.id,
            workspaceId: input.workspaceId,
          },
        },
      })

      if (existingMembership) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'User is already a member of this workspace',
        })
      }

      // Add user to workspace
      const newMembership = await ctx.db.workspaceMember.create({
        data: {
          userId: user.id,
          workspaceId: input.workspaceId,
          role: input.role,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
      })

      return newMembership
    }),

  // Remove user from workspace
  removeUser: protectedProcedure
    .input(
      z.object({
        workspaceId: z.string(),
        userId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check permissions
      const membership = await ctx.db.workspaceMember.findFirst({
        where: {
          workspaceId: input.workspaceId,
          userId: ctx.session.user.id,
          role: { in: ['OWNER', 'ADMIN'] },
        },
      })

      if (!membership) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient permissions',
        })
      }

      // Can't remove workspace owner
      const targetMembership = await ctx.db.workspaceMember.findUnique({
        where: {
          userId_workspaceId: {
            userId: input.userId,
            workspaceId: input.workspaceId,
          },
        },
      })

      if (targetMembership?.role === 'OWNER') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Cannot remove workspace owner',
        })
      }

      await ctx.db.workspaceMember.delete({
        where: {
          userId_workspaceId: {
            userId: input.userId,
            workspaceId: input.workspaceId,
          },
        },
      })

      return { success: true }
    }),

  // Update user role in workspace
  updateUserRole: protectedProcedure
    .input(
      z.object({
        workspaceId: z.string(),
        userId: z.string(),
        role: z.enum(['MEMBER', 'ADMIN', 'VIEWER']),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check permissions
      const membership = await ctx.db.workspaceMember.findFirst({
        where: {
          workspaceId: input.workspaceId,
          userId: ctx.session.user.id,
          role: { in: ['OWNER', 'ADMIN'] },
        },
      })

      if (!membership) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient permissions',
        })
      }

      const updatedMembership = await ctx.db.workspaceMember.update({
        where: {
          userId_workspaceId: {
            userId: input.userId,
            workspaceId: input.workspaceId,
          },
        },
        data: {
          role: input.role,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
      })

      return updatedMembership
    }),

  // Leave workspace
  leaveWorkspace: protectedProcedure
    .input(z.object({ workspaceId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const membership = await ctx.db.workspaceMember.findUnique({
        where: {
          userId_workspaceId: {
            userId: ctx.session.user.id,
            workspaceId: input.workspaceId,
          },
        },
      })

      if (!membership) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'You are not a member of this workspace',
        })
      }

      if (membership.role === 'OWNER') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Workspace owners cannot leave. Transfer ownership first.',
        })
      }

      await ctx.db.workspaceMember.delete({
        where: {
          userId_workspaceId: {
            userId: ctx.session.user.id,
            workspaceId: input.workspaceId,
          },
        },
      })

      return { success: true }
    }),
})
