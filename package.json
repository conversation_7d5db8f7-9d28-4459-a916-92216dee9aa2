{"name": "nexusai", "version": "0.1.0", "private": true, "description": "Next-generation AI chat interface platform with multi-provider support and advanced features", "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/nexusai.git"}, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "format": "prettier --write .", "format:check": "prettier --check .", "postinstall": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^1.4.0", "@hookform/resolvers": "^3.3.4", "@prisma/client": "^5.9.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.20.5", "@trpc/client": "^10.45.1", "@trpc/next": "^10.45.1", "@trpc/react-query": "^10.45.1", "@trpc/server": "^10.45.1", "@types/node": "^20.11.19", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.17", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.1", "date-fns": "^3.3.1", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "framer-motion": "^11.0.5", "ioredis": "^5.3.2", "lucide-react": "^0.323.0", "next": "14.1.0", "next-auth": "^4.24.6", "next-themes": "^0.2.1", "openai": "^4.28.0", "postcss": "^8.4.35", "prisma": "^5.9.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.12.1", "remark-gfm": "^4.0.0", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "superjson": "^2.2.1", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3", "zod": "^3.22.4", "zustand": "^4.5.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/react-syntax-highlighter": "^15.5.11", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tsx": "^4.7.1"}, "engines": {"node": ">=18.0.0"}}