import { createTRPCRouter } from '@/server/api/trpc'
import { authRouter } from './routers/auth'
import { chatRouter } from './routers/chat'
import { aiRouter } from './routers/ai'
import { promptRouter } from './routers/prompt'
import { workspaceRouter } from './routers/workspace'
import { analyticsRouter } from './routers/analytics'
import { userRouter } from './routers/user'
import { exportRouter } from './routers/export'

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  auth: authRouter,
  chat: chatRouter,
  ai: aiRouter,
  prompt: promptRouter,
  workspace: workspaceRouter,
  analytics: analyticsRouter,
  user: userRouter,
  export: exportRouter,
})

// export type definition of API
export type AppRouter = typeof appRouter
