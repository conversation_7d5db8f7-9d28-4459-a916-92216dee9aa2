import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure, protectedRateLimitedProcedure } from '@/server/api/trpc'
import { dbUtils } from '@/lib/db'

export const chatRouter = createTRPCRouter({
  // Get all conversations for the current user
  getConversations: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
        workspaceId: z.string().optional(),
        status: z.enum(['ACTIVE', 'ARCHIVED', 'DELETED']).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const conversations = await ctx.db.conversation.findMany({
        where: {
          userId: ctx.session.user.id,
          workspaceId: input.workspaceId,
          status: input.status || 'ACTIVE',
        },
        include: {
          messages: {
            take: 1,
            orderBy: { createdAt: 'desc' },
          },
          _count: {
            select: {
              messages: true,
              branches: true,
            },
          },
        },
        orderBy: { updatedAt: 'desc' },
        take: input.limit,
        skip: input.offset,
      })

      return conversations
    }),

  // Get a specific conversation with all messages
  getConversation: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const conversation = await dbUtils.getConversationWithMessages(
        input.id,
        ctx.session.user.id
      )

      if (!conversation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Conversation not found',
        })
      }

      return conversation
    }),

  // Create a new conversation
  createConversation: protectedProcedure
    .input(
      z.object({
        title: z.string().min(1).max(200),
        model: z.string(),
        provider: z.enum(['OPENAI', 'ANTHROPIC', 'GOOGLE', 'LOCAL', 'CUSTOM']),
        workspaceId: z.string().optional(),
        metadata: z.record(z.any()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const conversation = await ctx.db.conversation.create({
        data: {
          title: input.title,
          userId: ctx.session.user.id,
          model: input.model,
          provider: input.provider,
          workspaceId: input.workspaceId,
          metadata: input.metadata,
        },
        include: {
          messages: true,
          _count: {
            select: {
              messages: true,
              branches: true,
            },
          },
        },
      })

      // Log the action
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'CONVERSATION_CREATED',
          resource: 'CONVERSATION',
          details: {
            conversationId: conversation.id,
            title: conversation.title,
            provider: conversation.provider,
            model: conversation.model,
          },
        },
      })

      return conversation
    }),

  // Update conversation
  updateConversation: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        title: z.string().min(1).max(200).optional(),
        status: z.enum(['ACTIVE', 'ARCHIVED', 'DELETED']).optional(),
        metadata: z.record(z.any()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Verify ownership
      const existingConversation = await ctx.db.conversation.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
      })

      if (!existingConversation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Conversation not found',
        })
      }

      const conversation = await ctx.db.conversation.update({
        where: { id: input.id },
        data: {
          title: input.title,
          status: input.status,
          metadata: input.metadata,
          updatedAt: new Date(),
        },
      })

      return conversation
    }),

  // Delete conversation
  deleteConversation: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Verify ownership
      const existingConversation = await ctx.db.conversation.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
      })

      if (!existingConversation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Conversation not found',
        })
      }

      await ctx.db.conversation.delete({
        where: { id: input.id },
      })

      // Log the action
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'CONVERSATION_DELETED',
          resource: 'CONVERSATION',
          details: {
            conversationId: input.id,
            title: existingConversation.title,
          },
        },
      })

      return { success: true }
    }),

  // Create a conversation branch
  createBranch: protectedProcedure
    .input(
      z.object({
        parentId: z.string(),
        branchName: z.string().min(1).max(100),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const branch = await dbUtils.createConversationBranch(
        input.parentId,
        input.branchName,
        ctx.session.user.id
      )

      // Log the action
      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          action: 'CONVERSATION_BRANCHED',
          resource: 'CONVERSATION',
          details: {
            parentId: input.parentId,
            branchId: branch.id,
            branchName: input.branchName,
          },
        },
      })

      return branch
    }),

  // Send a message
  sendMessage: protectedRateLimitedProcedure
    .input(
      z.object({
        conversationId: z.string(),
        content: z.string().min(1),
        role: z.enum(['USER', 'ASSISTANT', 'SYSTEM']).default('USER'),
        parentId: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Verify conversation ownership
      const conversation = await ctx.db.conversation.findFirst({
        where: {
          id: input.conversationId,
          userId: ctx.session.user.id,
        },
      })

      if (!conversation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Conversation not found',
        })
      }

      // Check user credits
      if (ctx.session.user.credits <= 0) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient credits',
        })
      }

      const message = await ctx.db.message.create({
        data: {
          conversationId: input.conversationId,
          content: input.content,
          role: input.role,
          parentId: input.parentId,
        },
      })

      // Update conversation timestamp
      await ctx.db.conversation.update({
        where: { id: input.conversationId },
        data: { updatedAt: new Date() },
      })

      return message
    }),

  // Update a message
  updateMessage: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        content: z.string().min(1),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Verify message ownership through conversation
      const message = await ctx.db.message.findFirst({
        where: {
          id: input.id,
          conversation: {
            userId: ctx.session.user.id,
          },
        },
      })

      if (!message) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Message not found',
        })
      }

      const updatedMessage = await ctx.db.message.update({
        where: { id: input.id },
        data: {
          content: input.content,
          isEdited: true,
          updatedAt: new Date(),
        },
      })

      return updatedMessage
    }),

  // Delete a message
  deleteMessage: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Verify message ownership through conversation
      const message = await ctx.db.message.findFirst({
        where: {
          id: input.id,
          conversation: {
            userId: ctx.session.user.id,
          },
        },
      })

      if (!message) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Message not found',
        })
      }

      await ctx.db.message.delete({
        where: { id: input.id },
      })

      return { success: true }
    }),

  // Get conversation tree (for branching visualization)
  getConversationTree: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const conversation = await ctx.db.conversation.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
        include: {
          branches: {
            include: {
              messages: {
                take: 1,
                orderBy: { createdAt: 'desc' },
              },
              _count: {
                select: { messages: true },
              },
            },
          },
          messages: {
            take: 1,
            orderBy: { createdAt: 'desc' },
          },
          _count: {
            select: { messages: true },
          },
        },
      })

      if (!conversation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Conversation not found',
        })
      }

      return conversation
    }),
})
