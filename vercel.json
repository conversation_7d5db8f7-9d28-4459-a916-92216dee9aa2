{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"DATABASE_URL": "@database-url", "NEXTAUTH_SECRET": "@nextauth-secret", "NEXTAUTH_URL": "@nextauth-url", "OPENAI_API_KEY": "@openai-api-key", "ANTHROPIC_API_KEY": "@anthropic-api-key", "GOOGLE_AI_API_KEY": "@google-ai-api-key", "GOOGLE_CLIENT_ID": "@google-client-id", "GOOGLE_CLIENT_SECRET": "@google-client-secret", "GITHUB_CLIENT_ID": "@github-client-id", "GITHUB_CLIENT_SECRET": "@github-client-secret", "REDIS_URL": "@redis-url"}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/socket.io/(.*)", "destination": "/api/socket/$1"}]}